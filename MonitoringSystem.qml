import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12
import QtCharts 2.3

Page {
    id: monitoringSystem
    
    // 信号定义
    signal navigateBack()

    // 分解炉选择按钮组
    ButtonGroup {
        id: boilerButtonGroup
        exclusive: true
    }

    // 返回首页状态标志
    property bool isReturningHome: false

    // 添加回可能被引用的属性（简化版本，避免程序崩溃）
    property bool isBoilerSwitching: false
    property string pendingBoilerSwitch: ""
    property string selectedBoilerUI: ""

    // 时间范围选择相关属性
    property int currentTimeRange: 60  // 默认1小时
    property var timeRangeOptions: [
        {label: "1小时", minutes: 60},
        {label: "8小时", minutes: 480},
        {label: "12小时", minutes: 720},
        {label: "24小时", minutes: 1440}
    ]

    // 历史数据相关属性
    property bool isHistoricalMode: false  // 是否处于历史数据模式
    property string currentHistoricalDate: ""  // 当前选择的历史日期
    property var historicalData: []  // 历史数据缓存
    property int totalHistoricalPoints: 0  // 历史数据总点数
    property bool needsSlider: false  // 是否需要滑块
    property double sliderPosition: 0.0  // 滑块位置 (0.0-1.0)
    property int currentSliderOffset: 0  // 当前滑块偏移量（数据点数）

    // 智能图表更新定时器 - 统一的更新机制
    Timer {
        id: chartUpdateTimer
        interval: {
            // 根据当前设备的采集间隔动态调整更新频率
            var collectionInterval = monitorWindow.dataSource.getCurrentCollectionInterval()
            return Math.max(collectionInterval * 1000, 3000)  // 最少3秒更新一次
        }
        repeat: true
        running: monitorWindow.dataSource.isRunning && !isHistoricalMode  // 历史数据模式下停止定时器

        property int updateCounter: 0  // 更新计数器

        onTriggered: {
            updateCounter++

            // 每10次增量更新后执行一次全量更新，避免数据偏差
            if (updateCounter % 10 === 0) {
                fullUpdateChartData()  // 全量更新
            } else {
                updateChartData()  // 增量更新
            }
        }
    }

    // 智能增量更新 - 避免不必要的重绘
    function updateChartData() {
        // 历史数据模式下不进行实时更新
        if (isHistoricalMode) {
            return
        }

        if (monitorWindow.dataSource.currentBoiler !== "" &&
            monitorWindow.dataSource.isDataConnected) {
            // 使用优化的增量更新，传入当前时间范围
            monitorWindow.dataSource.updateChartIncremental(
                smokeO2Series, smokeCOSeries, currentTimeRange
            )
        }
    }

    // 全量更新函数 - 仅在必要时使用
    function fullUpdateChartData() {
        // 历史数据模式下不进行实时更新
        if (isHistoricalMode) {
            return
        }

        if (monitorWindow.dataSource.currentBoiler !== "" &&
            monitorWindow.dataSource.isDataConnected) {
            // 全量更新，使用高效的replace方法，传入当前时间范围
            monitorWindow.dataSource.updateSmokeChartSeriesWithMinutes(
                smokeO2Series, smokeCOSeries, currentTimeRange
            )
        }
    }

    // 时间范围切换函数
    function switchTimeRange(newTimeRange) {
        if (currentTimeRange !== newTimeRange) {
            currentTimeRange = newTimeRange
            // 更新图表轴范围
            smokeAxisX.max = newTimeRange
            // tickCount会通过ValueAxis中的绑定自动更新，不需要手动设置

            if (isHistoricalMode) {
                // 历史数据模式：重新计算滑块需求和更新图表
                updateHistoricalChart()
            } else {
                // 实时数据模式：强制全量更新图表数据
                fullUpdateChartData()
            }
        }
    }

    // 历史数据相关函数
    function loadHistoricalData(date) {
        console.log("加载历史数据:", date)
        var dateObj = new Date(date)
        var historicalDataList = csvReader.readDataByDate(dateObj)

        if (historicalDataList.length === 0) {
            console.log("没有找到历史数据")
            return false
        }

        historicalData = historicalDataList
        totalHistoricalPoints = historicalDataList.length
        currentHistoricalDate = date
        isHistoricalMode = true

        // 计算是否需要滑块
        calculateSliderNeeds()

        // 更新图表
        updateHistoricalChart()

        console.log("历史数据加载完成，总点数:", totalHistoricalPoints, "需要滑块:", needsSlider)
        return true
    }

    function calculateSliderNeeds() {
        // 根据当前时间范围计算需要的点数
        var requiredPoints = getRequiredPointsForTimeRange(currentTimeRange)

        // 如果历史数据点数超过当前时间范围的限制，则需要滑块
        needsSlider = totalHistoricalPoints > requiredPoints

        // 重置滑块位置
        sliderPosition = 0.0
        currentSliderOffset = 0

        console.log("计算滑块需求 - 总点数:", totalHistoricalPoints, "需要点数:", requiredPoints, "需要滑块:", needsSlider)
    }

    function getRequiredPointsForTimeRange(timeRangeMinutes) {
        // 每3秒一个点，计算指定时间范围需要的点数
        return timeRangeMinutes * 60 / 3  // 60分钟=1200点，480分钟=9600点，以此类推
    }

    function updateHistoricalChart() {
        if (!isHistoricalMode || historicalData.length === 0) {
            return
        }

        var requiredPoints = getRequiredPointsForTimeRange(currentTimeRange)
        var startIndex = currentSliderOffset
        var endIndex = Math.min(startIndex + requiredPoints, totalHistoricalPoints)

        // 清空现有数据
        smokeO2Series.clear()
        smokeCOSeries.clear()

        // 添加历史数据到图表
        var timeInterval = 3.0 / 60.0  // 3秒 = 0.05分钟

        for (var i = startIndex; i < endIndex; i++) {
            var dataPoint = historicalData[i]
            var timeMinutes = (i - startIndex) * timeInterval

            // 解析数据 - 根据CSV文件的实际列名
            var o2Value = parseFloat(dataPoint["O2(%)"] || dataPoint["氧量"] || dataPoint["o2"] || 0)
            var coValue = parseFloat(dataPoint["CO(ppm)"] || dataPoint["CO含量"] || dataPoint["co"] || 0)

            smokeO2Series.append(timeMinutes, o2Value)
            smokeCOSeries.append(timeMinutes, coValue)
        }

        console.log("历史图表更新完成，显示点数:", endIndex - startIndex, "起始索引:", startIndex)
    }

    function onSliderPositionChanged(position) {
        if (!needsSlider || !isHistoricalMode) {
            return
        }

        sliderPosition = position

        // 计算偏移量
        var requiredPoints = getRequiredPointsForTimeRange(currentTimeRange)
        var maxOffset = Math.max(0, totalHistoricalPoints - requiredPoints)
        currentSliderOffset = Math.floor(maxOffset * position)

        // 更新图表
        updateHistoricalChart()

        console.log("滑块位置更新:", position, "偏移量:", currentSliderOffset)
    }

    function returnToRealTime() {
        isHistoricalMode = false
        currentHistoricalDate = ""
        historicalData = []
        totalHistoricalPoints = 0
        needsSlider = false
        sliderPosition = 0.0
        currentSliderOffset = 0

        // 恢复实时数据显示
        fullUpdateChartData()

        console.log("返回实时数据模式")
    }

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回首页"
                enabled: !isReturningHome
                onClicked: {
                    if (isReturningHome) return

                    isReturningHome = true

                    // 直接触发页面切换
                    stackView.pop()

                    // 简单的清理操作
                    Qt.callLater(function() {
                        performQuickCleanup()
                    })
                }
            }
            Label {
                text: "锅炉燃烧数据监控系统"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }

            Button {
                text: "采集配置"
                onClicked: {
                    stackView.push(configPage)
                }
                background: Rectangle {
                    color: parent.pressed ? "#1976d2" : "#2196f3"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: monitorWindow.dataSource.isRunning ? "停止监控" : "开始监控"
                onClicked: {
                    if (monitorWindow.dataSource.isRunning) {
                        monitorWindow.stopMonitoring()
                    } else {
                        monitorWindow.startMonitoring()
                    }
                }
                background: Rectangle {
                    color: monitorWindow.dataSource.isRunning ? "#f44336" : "#4caf50"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Button {
                text: "清空数据"
                onClicked: monitorWindow.clearAllData()
                background: Rectangle {
                    color: parent.pressed ? "#ff9800" : "#ffc107"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // 主要内容区域 - 水平布局
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 20

            // 烟气数据区域 - 现在占据全宽
            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true

                // 限制外层滚动方向，只允许垂直滚动
                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded

                // 设置滚动行为
                contentWidth: -1  // 禁用水平滚动
                clip: true

                ColumnLayout {
                    width: parent.width
                    spacing: 20

                    // 锅炉选择区域 - 已注释
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 20

                            Label {
                                text: "选择锅炉:"
                                font.pixelSize: 16
                                font.bold: true
                                color: "#333333"
                            }

                            // 动态生成锅炉按钮
                            Repeater {
                                model: monitorWindow.dataSource.boilerList

                                Button {
                                    text: modelData
                                    Layout.preferredWidth: 100
                                    Layout.preferredHeight: 40
                                    checkable: true
                                    // 使用本地UI状态来控制选中状态，提供即时反馈
                                    checked: selectedBoilerUI === modelData
                                    ButtonGroup.group: boilerButtonGroup

                                    background: Rectangle {
                                        color: parent.checked ? "#E3F2FD" : "#ffffff"
                                        border.color: parent.checked ? "#2196f3" : "#ddd"
                                        border.width: 2
                                        radius: 8
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: parent.checked ? "#2196f3" : "#333333"
                                        font.pixelSize: 14
                                        font.bold: parent.checked
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }

                                    onClicked: {
                                        // 简化：直接更新锅炉选择
                                        selectedBoilerUI = modelData
                                        monitorWindow.dataSource.currentBoiler = modelData
                                    }
                                }
                            }

                            // 历史数据查看区域 - 启用功能
                            RowLayout {
                                spacing: 10

                                Label {
                                    text: "历史数据:"
                                    font.pixelSize: 14
                                    color: "#666666"
                                }

                                ComboBox {
                                    id: historicalDateCombo
                                    Layout.preferredWidth: 120
                                    Layout.preferredHeight: 30
                                    model: historicalDatesModel
                                    displayText: isHistoricalMode ? currentHistoricalDate : "选择日期"
                                    font.pixelSize: 12
                                    enabled: true

                                    onActivated: {
                                        if (index >= 0 && index < historicalDatesModel.count) {
                                            var selectedDate = historicalDatesModel.get(index).date
                                            loadHistoricalData(selectedDate)
                                        }
                                    }
                                }

                                Button {
                                    text: "刷新"
                                    Layout.preferredWidth: 50
                                    Layout.preferredHeight: 30
                                    font.pixelSize: 10
                                    enabled: true

                                    background: Rectangle {
                                        color: parent.pressed ? "#e0e0e0" : "#f5f5f5"
                                        border.color: "#cccccc"
                                        border.width: 1
                                        radius: 3
                                    }

                                    onClicked: {
                                        refreshHistoricalDates()
                                    }
                                }

                                Button {
                                    text: "返回实时"
                                    Layout.preferredWidth: 80
                                    Layout.preferredHeight: 30
                                    font.pixelSize: 10
                                    visible: isHistoricalMode
                                    enabled: true

                                    background: Rectangle {
                                        color: parent.pressed ? "#388e3c" : "#4caf50"
                                        radius: 3
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: parent.font.pixelSize
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }

                                    onClicked: {
                                        returnToRealTime()
                                    }
                                }
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            // 显示当前选择的锅炉信息
                            RowLayout {
                                spacing: 8

                                Label {
                                    text: "当前: " + selectedBoilerUI
                                    font.pixelSize: 14
                                    font.bold: true
                                    color: "#2196f3"
                                }

                                // 简化的连接状态指示器
                                Rectangle {
                                    width: 12
                                    height: 12
                                    radius: 6
                                    color: "#4caf50"
                                    visible: selectedBoilerUI !== ""
                                }

                                Label {
                                    text: "已连接"
                                    font.pixelSize: 12
                                    color: "#4caf50"
                                    visible: selectedBoilerUI !== ""
                                }
                            }
                        }
                    }

                    // 烟气数据区域
                    Rectangle {
                        Layout.fillWidth: true
                        height: 580
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: 15

                            RowLayout {
                                Layout.fillWidth: true

                                Label {
                                    text: "烟气检测设备实时数据"
                                    font.pixelSize: 20
                                    font.bold: true
                                    color: "#333333"
                                    Layout.fillWidth: true
                                }

                                Rectangle {
                                    width: 12
                                    height: 12
                                    radius: 6
                                    color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                }

                                Label {
                                    text: monitorWindow.dataSource.connectionStatus
                                    font.pixelSize: 14
                                    color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                    font.bold: true
                                }
                            }

                            // 当前烟气数据显示
                            Rectangle {
                                Layout.fillWidth: true
                                height: 80
                                color: monitorWindow.dataSource.isDataConnected ? "#e8f5e8" : "#ffebee"
                                radius: 8
                                border.color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                border.width: 1

                                // 根据连接状态显示不同内容
                                StackLayout {
                                    anchors.fill: parent
                                    anchors.margins: 15
                                    currentIndex: monitorWindow.dataSource.isDataConnected ? 0 : 1

                                    // 有数据连接时显示实际数据
                                    RowLayout {
                                        spacing: 30

                                        // 氧量
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "氧量："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                      (monitorWindow.dataSource.smokeTableData[0].o2 + "%") : "--"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#388e3c"
                                            }
                                        }

                                        // CO含量
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "CO含量："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                      (monitorWindow.dataSource.smokeTableData[0].co + "ppm") : "--"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#ff9800"
                                            }
                                        }

                                        // 冷凝器温度
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "冷凝器温度："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.currentTemperature || "0.0℃"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#e91e63"
                                            }
                                        }

                                        // 压力表
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "压力表："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.currentVoltage || "0.0kPa"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#2196f3"
                                            }
                                        }

                                        // 抽气泵电流
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "抽气泵电流："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.currentCurrent || "0.000A"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#ff5722"
                                            }
                                        }

                                        // 反吹反馈
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "反吹反馈："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                      (parseInt(monitorWindow.dataSource.smokeTableData[0].switch1) === 1 ? "运行" : "停止") : "停止"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: monitorWindow.dataSource.smokeSwitch1Data.length > 0 &&
                                                       monitorWindow.dataSource.smokeSwitch1Data[monitorWindow.dataSource.smokeSwitch1Data.length - 1].y === 1 ? "#4caf50" : "#f44336"
                                            }
                                        }
                                    }

                                    // 无数据连接时显示提示信息
                                    ColumnLayout {
                                        anchors.centerIn: parent
                                        spacing: 10

                                        Label {
                                            text: "⚠️ 未接入数据采集设备"
                                            font.pixelSize: 18
                                            font.bold: true
                                            color: "#f44336"
                                            Layout.alignment: Qt.AlignHCenter
                                        }

                                        Label {
                                            text: "请检查串口连接和设备配置"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.alignment: Qt.AlignHCenter
                                        }
                                    }
                                }
                            }

                            // 图表容器，包含图表和滚动条
                            ColumnLayout {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                spacing: 0

                                // 图表和滚动条的容器
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: "transparent"

                                    // 简化的ChartView，用于Qt 5.12兼容性
                                    ChartView {
                                        id: smokeChart
                                        anchors.fill: parent
                                        antialiasing: true
                                        backgroundColor: "#f8f9fa"

                                    title: {
                                        var timeLabel = ""
                                        switch (currentTimeRange) {
                                            case 60:
                                                timeLabel = "1小时"
                                                break
                                            case 480:
                                                timeLabel = "8小时"
                                                break
                                            case 720:
                                                timeLabel = "12小时"
                                                break
                                            case 1440:
                                                timeLabel = "24小时"
                                                break
                                            default:
                                                timeLabel = "1小时"
                                                break
                                        }
                                        var modeLabel = isHistoricalMode ? "历史数据" : "实时数据"
                                        var dateLabel = isHistoricalMode ? " - " + currentHistoricalDate : ""
                                        return "烟气检测设备" + modeLabel + "曲线 (" + timeLabel + ")" + dateLabel
                                    }
                                    titleFont.pixelSize: 16
                                    titleFont.bold: true

                                    legend.alignment: Qt.AlignBottom
                                    legend.font.pixelSize: 12

                                    // 缩放相关属性已删除，仅保留UI样式



                                    ValueAxis {
                                        id: smokeAxisX
                                        // 优化：统一使用分钟作为时间单位
                                        titleText: "时间 (分钟)"

                                        // 动态调整显示范围
                                        property double windowSize: currentTimeRange  // 动态时间窗口

                                        tickCount: {
                                            // 根据时间范围设置合适的刻度数量
                                            switch (currentTimeRange) {
                                                case 60:    // 1小时，每10分钟一个刻度：0, 10, 20, 30, 40, 50, 60
                                                    return 7
                                                case 480:   // 8小时，每小时一个刻度：0, 60, 120, 180, 240, 300, 360, 420, 480
                                                    return 9
                                                case 720:   // 12小时，每2小时一个刻度：0, 120, 240, 360, 480, 600, 720
                                                    return 7
                                                case 1440:  // 24小时，每4小时一个刻度：0, 240, 480, 720, 960, 1200, 1440
                                                    return 7
                                                default:
                                                    return 7
                                            }
                                        }

                                        labelFormat: "%.0f"  // 显示整数分钟

                                        // 动态X轴范围 - 根据选择的时间范围调整
                                        min: 0
                                        max: windowSize  // 动态时间范围
                                    }

                                    // 相对时间轴不需要复杂的时间范围计算
                                    // X轴范围已经在ValueAxis中直接设置

                                    // 缩放变化处理已删除

                                // 左侧Y轴：O₂ 氧量 (0-25%)
                                ValueAxis {
                                    id: smokeAxisY_Left
                                    min: 0
                                    max: 25
                                    titleText: "O₂ (%)"
                                    labelFormat: "%.1f"
                                    color: "#388e3c"
                                }

                                // 右侧Y轴：CO (0-7000)
                                ValueAxis {
                                    id: smokeAxisY_Right
                                    min: 0
                                    max: 7000
                                    titleText: "CO(ppm)"
                                    labelFormat: "%.0f"
                                    color: "#666666"
                                }



                                LineSeries {
                                    id: smokeO2Series
                                    name: "O₂ (%)"
                                    color: "#388e3c"
                                    width: 2
                                    axisX: smokeAxisX
                                    axisY: smokeAxisY_Left
                                }

                                LineSeries {
                                    id: smokeCOSeries
                                    name: "CO (ppm)"
                                    color: "#ff9800"
                                    width: 2
                                    axisX: smokeAxisX
                                    axisYRight: smokeAxisY_Right
                                }

                                // 简化的数据更新信号连接
                                Connections {
                                    target: monitorWindow.dataSource
                                    function onChartDataUpdated() {
                                        // 禁用信号触发的更新，避免与定时器冲突
                                        // 只在设备切换时才进行全量更新
                                    }
                                }

                                    // 鼠标交互区域
                                    MouseArea {
                                        anchors.fill: parent
                                        acceptedButtons: Qt.NoButton
                                        hoverEnabled: true
                                    }

                                    // 相对时间轴不需要定时更新

                                    Component.onCompleted: {
                                        // 初始化时使用全量更新 - 简化为30分钟视图
                                        fullUpdateChartData()
                                    }
                                }
                            }

                                // 缩放控制按钮已删除

                                // 时间范围选择控件和滑块
                                ColumnLayout {
                                    Layout.fillWidth: true
                                    spacing: 10

                                    // 时间范围选择
                                    RowLayout {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 40
                                        spacing: 15

                                        Label {
                                            text: "时间范围:"
                                            font.pixelSize: 12
                                            color: "#666666"
                                        }

                                        // 动态生成时间范围按钮
                                        Repeater {
                                            model: timeRangeOptions

                                            Button {
                                                text: modelData.label
                                                Layout.preferredWidth: 70
                                                Layout.preferredHeight: 30
                                                font.pixelSize: 10

                                                property bool isSelected: currentTimeRange === modelData.minutes

                                                onClicked: {
                                                    switchTimeRange(modelData.minutes)
                                                }

                                                background: Rectangle {
                                                    color: parent.isSelected ? "#2196f3" : (parent.pressed ? "#e0e0e0" : "#f5f5f5")
                                                    border.color: parent.isSelected ? "#1976d2" : "#cccccc"
                                                    border.width: 1
                                                    radius: 3
                                                }

                                                contentItem: Text {
                                                    text: parent.text
                                                    color: parent.isSelected ? "white" : "#333333"
                                                    font.pixelSize: parent.font.pixelSize
                                                    font.bold: parent.isSelected
                                                    horizontalAlignment: Text.AlignHCenter
                                                    verticalAlignment: Text.AlignVCenter
                                                }
                                            }
                                        }

                                        Item {
                                            Layout.fillWidth: true
                                        }

                                        Label {
                                            text: {
                                                var timeLabel = ""
                                                switch (currentTimeRange) {
                                                    case 60:
                                                        timeLabel = "1小时"
                                                        break
                                                    case 480:
                                                        timeLabel = "8小时"
                                                        break
                                                    case 720:
                                                        timeLabel = "12小时"
                                                        break
                                                    case 1440:
                                                        timeLabel = "24小时"
                                                        break
                                                    default:
                                                        timeLabel = "1小时"
                                                        break
                                                }
                                                var modeText = isHistoricalMode ? "历史模式" : "实时模式"
                                                return modeText + " - " + timeLabel
                                            }
                                            font.pixelSize: 11
                                            color: isHistoricalMode ? "#ff9800" : "#666666"
                                            font.bold: true
                                        }
                                    }

                                    // 历史数据滑块控制
                                    RowLayout {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 40
                                        spacing: 10
                                        visible: isHistoricalMode && needsSlider

                                        Label {
                                            text: "数据范围:"
                                            font.pixelSize: 12
                                            color: "#666666"
                                        }

                                        Slider {
                                            id: historicalSlider
                                            Layout.fillWidth: true
                                            from: 0.0
                                            to: 1.0
                                            value: sliderPosition
                                            stepSize: 0.01

                                            onValueChanged: {
                                                if (Math.abs(value - sliderPosition) > 0.001) {
                                                    onSliderPositionChanged(value)
                                                }
                                            }

                                            background: Rectangle {
                                                x: historicalSlider.leftPadding
                                                y: historicalSlider.topPadding + historicalSlider.availableHeight / 2 - height / 2
                                                implicitWidth: 200
                                                implicitHeight: 4
                                                width: historicalSlider.availableWidth
                                                height: implicitHeight
                                                radius: 2
                                                color: "#e0e0e0"

                                                Rectangle {
                                                    width: historicalSlider.visualPosition * parent.width
                                                    height: parent.height
                                                    color: "#2196f3"
                                                    radius: 2
                                                }
                                            }

                                            handle: Rectangle {
                                                x: historicalSlider.leftPadding + historicalSlider.visualPosition * (historicalSlider.availableWidth - width)
                                                y: historicalSlider.topPadding + historicalSlider.availableHeight / 2 - height / 2
                                                implicitWidth: 20
                                                implicitHeight: 20
                                                radius: 10
                                                color: historicalSlider.pressed ? "#1976d2" : "#2196f3"
                                                border.color: "#ffffff"
                                                border.width: 2
                                            }
                                        }

                                        Label {
                                            text: {
                                                if (!isHistoricalMode || !needsSlider) return ""
                                                var requiredPoints = getRequiredPointsForTimeRange(currentTimeRange)
                                                var startPoint = currentSliderOffset + 1
                                                var endPoint = Math.min(currentSliderOffset + requiredPoints, totalHistoricalPoints)
                                                return startPoint + "-" + endPoint + "/" + totalHistoricalPoints
                                            }
                                            font.pixelSize: 10
                                            color: "#666666"
                                            Layout.preferredWidth: 80
                                        }
                                    }
                                }
                            }
                            }
                        }
                    }

                    // 烟气数据表格
                    Rectangle {
                        Layout.fillWidth: true
                        height: 350
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: 15

                            RowLayout {
                                Layout.fillWidth: true

                                Label {
                                    text: "烟气数据记录"
                                    font.pixelSize: 18
                                    font.bold: true
                                    color: "#333333"
                                    Layout.fillWidth: true
                                }

                                Label {
                                    text: {
                                        var count = monitorWindow.dataSource.smokeTableData.length
                                        return "实时数据 (" + count + " 条记录)"
                                    }
                                    font.pixelSize: 12
                                    color: "#666666"
                                }
                            }

                            // 固定表头
                            Rectangle {
                                Layout.fillWidth: true
                                height: 40
                                color: "#e8f5e8"
                                border.color: "#4caf50"
                                border.width: 1
                                z: 1

                                Row {
                                    anchors.fill: parent
                                    anchors.margins: 10
                                    spacing: 10

                                    Label { text: "时间"; width: 160; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "氧量(%)"; width: 70; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "CO(ppm)"; width: 80; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "冷凝器温度(℃)"; width: 90; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "压力表(kPa)"; width: 80; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "抽气泵电流(A)"; width: 90; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "反吹反馈"; width: 60; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                }
                            }

                            Rectangle {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: "#ffffff"
                                border.color: "#e0e0e0"
                                border.width: 1

                                StackLayout {
                                    anchors.fill: parent
                                    currentIndex: (monitorWindow.dataSource.isDataConnected && monitorWindow.dataSource.smokeTableData.length > 0) ? 0 : 1

                                    // 有数据时显示表格
                                    ListView {
                                        model: monitorWindow.dataSource.smokeTableData
                                        clip: true
                                        boundsBehavior: Flickable.StopAtBounds

                                        // 防止滚动事件向上传播到外层ScrollView
                                        flickableDirection: Flickable.VerticalFlick
                                        interactive: true

                                        // 滚动条
                                        ScrollBar.vertical: ScrollBar {
                                            active: true
                                            policy: ScrollBar.AsNeeded
                                        }

                                        // 添加鼠标区域来拦截滚动事件
                                        MouseArea {
                                            anchors.fill: parent
                                            acceptedButtons: Qt.NoButton
                                            onWheel: {
                                                // 检查ListView是否可以处理滚动
                                                var listView = parent
                                                var canScrollUp = listView.contentY > 0
                                                var canScrollDown = listView.contentY < (listView.contentHeight - listView.height)

                                                // 如果ListView可以滚动，则阻止事件传播
                                                if ((wheel.angleDelta.y > 0 && canScrollUp) ||
                                                    (wheel.angleDelta.y < 0 && canScrollDown)) {
                                                    wheel.accepted = true

                                                    // 手动处理ListView滚动
                                                    var delta = wheel.angleDelta.y
                                                    var scrollAmount = delta > 0 ? -30 : 30  // 滚动步长
                                                    listView.contentY = Math.max(0,
                                                        Math.min(listView.contentHeight - listView.height,
                                                                listView.contentY + scrollAmount))
                                                } else {
                                                    // ListView已到边界，允许事件传播给外层ScrollView
                                                    wheel.accepted = false
                                                }
                                            }
                                        }

                                        delegate: Rectangle {
                                            width: parent ? parent.width : 800  // 防止parent为null的错误
                                            height: 35
                                            color: index % 2 === 0 ? "#ffffff" : "#f5f5f5"
                                            border.color: "#e0e0e0"
                                            border.width: index === 0 ? 0 : 1

                                            Row {
                                                anchors.fill: parent
                                                anchors.margins: 10
                                                spacing: 10

                                                Label {
                                                    text: modelData.time
                                                    width: 160
                                                    font.pixelSize: 10
                                                    color: "#333333"
                                                }
                                                Label {
                                                    text: modelData.o2 + "%"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#388e3c"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: modelData.co + "ppm"
                                                    width: 80
                                                    font.pixelSize: 10
                                                    color: "#ff9800"
                                                    font.bold: true
                                                }

                                                Label {
                                                    text: (modelData.temperature || "0.0") + "℃"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#e91e63"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (modelData.voltage || "0.0") + "V"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#2196f3"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (modelData.current || "0.000") + "A"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#ff5722"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (parseInt(modelData.switch1) === 1) ? "运行" : "停止"
                                                    width: 60
                                                    font.pixelSize: 10
                                                    color: (parseInt(modelData.switch1) === 1) ? "#4caf50" : "#f44336"
                                                    font.bold: true
                                                }
                                            }
                                        }
                                    }

                                    // 无数据时显示提示
                                    ColumnLayout {
                                        anchors.centerIn: parent
                                        spacing: 15

                                        Label {
                                            text: "📊 暂无数据记录"
                                            font.pixelSize: 16
                                            font.bold: true
                                            color: "#999999"
                                            Layout.alignment: Qt.AlignHCenter
                                        }

                                        Label {
                                            text: monitorWindow.dataSource.isDataConnected ?
                                                  "等待数据采集..." : "请先连接数据采集设备"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.alignment: Qt.AlignHCenter
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 简化的数据连接状态监听
    Connections {
        target: monitorWindow.dataSource
        function onCurrentBoilerChanged() {
            // 简单的状态同步，无需复杂的防抖逻辑
            // 数据源已经简化，直接同步即可
        }
    }

    // 历史数据相关函数
    function refreshHistoricalDates() {
        console.log("刷新历史数据日期列表")
        historicalDatesModel.clear()

        var availableDates = csvReader.getAvailableDates()
        console.log("找到历史数据日期:", availableDates.length, "个")

        for (var i = 0; i < availableDates.length; i++) {
            historicalDatesModel.append({
                "date": availableDates[i],
                "display": availableDates[i]
            })
        }
    }

    // 页面加载完成后自动开始监控
    Component.onCompleted: {
        // 启动实时监控
        monitorWindow.startMonitoring()

        // 简化的初始化
        Qt.callLater(function() {
            // 触发图表初始化（使用全量更新）
            fullUpdateChartData()

            // 刷新历史数据日期列表
            refreshHistoricalDates()
        })

        // MonitoringDataSource现在使用智能检测机制，QML端不再需要额外的延迟
    }

    // 优化的清理函数 - 减少不必要的操作
    function performQuickCleanup() {
        // 停止监控
        if (monitorWindow.dataSource.isRunning) {
            monitorWindow.stopMonitoring()
        }

        // 只在必要时清理图表数据
        if (smokeO2Series.count > 0 || smokeCOSeries.count > 0) {
            smokeO2Series.clear()
            smokeCOSeries.clear()
        }

        // 重置状态
        resetPageState()
    }

    // 重置页面状态
    function resetPageState() {
        isReturningHome = false
    }

    // 完整清理资源的函数 - 保留作为备用
    function performCleanup() {
        performQuickCleanup()
    }

    // 页面销毁时清理资源（保留作为备用）
    Component.onDestruction: {
        // 如果页面被强制销毁，只执行最关键的清理
        if (monitorWindow.dataSource.isRunning) {
            monitorWindow.stopMonitoring()
        }
        chartUpdateTimer.stop()
    }

    // 历史数据相关的数据模型 - 仅保留UI样式
    ListModel {
        id: historicalDatesModel
    }

    ListModel {
        id: historicalDataModel
    }
}
